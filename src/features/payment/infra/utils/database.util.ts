import { K<PERSON> } from 'knex';
import { IDatabase } from '@cbidigital/aqua-ddd';
import { PAYMENT_MODULE_INJECT_TOKENS } from '@shared';
import { IDatabaseUtil } from '@features/payment/domain';
import { DataSource, Lifecycle, Provider } from '@heronjs/common';

@Provider({
    token: PAYMENT_MODULE_INJECT_TOKENS.UTIL.DATABASE,
    scope: Lifecycle.Singleton,
})
export class DatabaseUtil implements IDatabaseUtil {
    private readonly _db: IDatabase;

    constructor(@DataSource() db: IDatabase) {
        this._db = db;
    }

    async startTrx(): Promise<Knex.Transaction> {
        const client = this._db.database();
        if (!client) throw new Error();
        const trx = await client?.transaction();
        return trx;
    }
}
