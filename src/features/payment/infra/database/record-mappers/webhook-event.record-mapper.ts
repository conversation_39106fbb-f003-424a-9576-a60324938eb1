import { <PERSON><PERSON>ecordMapper } from '@cbidigital/aqua-ddd';
import { WebhookEventDto } from '@features/payment/domain';
import { WebhookEventRecord } from '@features/payment/infra/database/records';

export class WebhookEventRecordMapper implements IRecordMapper<WebhookEventDto, WebhookEventRecord> {
    fromRecordToDto(record: WebhookEventRecord): WebhookEventDto {
        return {
            id: record.id,
            gateway: record.gateway,
            referenceId: record.reference_id,
            eventId: record.event_id,
            eventType: record.event_type,
            payload: record.payload,
            errorMessage: record.error_message,
            createdAt: record.created_at,
            updatedAt: record.updated_at,
        };
    }

    fromDtoToRecord(dto: WebhookEventDto): WebhookEventRecord {
        return {
            id: dto.id,
            gateway: dto.gateway,
            reference_id: dto.referenceId,
            event_id: dto.eventId,
            event_type: dto.eventType,
            payload: dto.payload,
            error_message: dto.errorMessage,
            created_at: dto.createdAt,
            updated_at: dto.updatedAt,
        };
    }

    fromRecordsToDtos(records: WebhookEventRecord[]): WebhookEventDto[] {
        return records.map((record) => this.fromRecordToDto(record));
    }

    fromDtosToRecords(dtos: WebhookEventDto[]): WebhookEventRecord[] {
        return dtos.map((dto) => this.fromDtoToRecord(dto));
    }
}
