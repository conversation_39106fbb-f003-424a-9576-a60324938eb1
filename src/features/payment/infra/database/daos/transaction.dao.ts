import { TransactionDto, TransactionLogDto } from '@features/payment/domain';
import { PAYMENT_MODULE_INJECT_TOKENS, TableNames } from 'src/shared';
import { Dao, DataSource, Lifecycle, Logger } from '@heronjs/common';
import { TransactionRecord, TransactionLogRecord } from '@features/payment/infra/database/records';
import {
    TransactionRecordMapper,
    TransactionLogRecordMapper,
} from '@features/payment/infra/database/record-mappers';
import { BaseDao, IBaseDao, IDatabase, RepositoryOptions } from '@cbidigital/aqua-ddd';

export interface ITransactionDao extends IBaseDao<TransactionDto, TransactionRecord> {
    update(entity: Partial<TransactionDto>, options?: RepositoryOptions): Promise<Partial<TransactionDto>>;
    findByGatewayTransactionId(gatewayTransactionId: string, options?: RepositoryOptions): Promise<TransactionDto | null>;
    findByUserId(userId: string, options?: RepositoryOptions): Promise<TransactionDto[]>;
    createTransactionLog(
        transactionId: string,
        transactionLog: TransactionLogDto,
        options?: RepositoryOptions,
    ): Promise<void>;
    updateTransactionLog(
        transactionId: string,
        logId: string,
        transactionLog: Partial<TransactionLogDto>,
        options?: RepositoryOptions,
    ): Promise<void>;
    deleteTransactionLog(transactionId: string, logId: string, options?: RepositoryOptions): Promise<void>;
}

@Dao({
    token: PAYMENT_MODULE_INJECT_TOKENS.DAO.TRANSACTION,
    scope: Lifecycle.Singleton,
})
export class TransactionDao extends BaseDao<TransactionDto, TransactionRecord> implements ITransactionDao {
    private readonly logger = new Logger(this.constructor.name);
    private readonly transactionLogMapper = new TransactionLogRecordMapper();

    constructor(@DataSource() db: IDatabase) {
        super({
            db,
            tableName: TableNames.TRANSACTION,
            recordMapper: new TransactionRecordMapper(),
        });
    }

    async update(dto: Partial<TransactionDto>, options: RepositoryOptions = {}): Promise<Partial<TransactionDto>> {
        const client = this.db.getClient();
        const record = this.recordMapper.fromDtoToRecord(dto as TransactionDto);
        const query = client.table(this.tableName).where('id', dto.id!).update(record);
        if (options.trx) query.transacting(options.trx);
        await query;
        return dto;
    }

    async findByGatewayTransactionId(gatewayTransactionId: string, options: RepositoryOptions = {}): Promise<TransactionDto | null> {
        const client = this.db.getClient();
        const query = client.table(this.tableName).where('gateway_transaction_id', gatewayTransactionId).first();
        if (options.trx) query.transacting(options.trx);
        const record = await query;

        if (!record) return null;

        // Load transaction logs
        const transactionLogsQuery = client.table(TableNames.TRANSACTION_LOG).where('transaction_id', record.id);
        if (options.trx) transactionLogsQuery.transacting(options.trx);
        const transactionLogs = await transactionLogsQuery;

        return (this.recordMapper as any).fromRecordToDto(
            record as TransactionRecord,
            transactionLogs as TransactionLogRecord[],
        ) as TransactionDto;
    }

    async findByUserId(userId: string, options: RepositoryOptions = {}): Promise<TransactionDto[]> {
        const client = this.db.getClient();
        const query = client.table(this.tableName).where('user_id', userId);
        if (options.trx) query.transacting(options.trx);
        const records = await query;

        const results: TransactionDto[] = [];
        for (const record of records) {
            // Load transaction logs for each transaction
            const transactionLogsQuery = client.table(TableNames.TRANSACTION_LOG).where('transaction_id', record.id);
            if (options.trx) transactionLogsQuery.transacting(options.trx);
            const transactionLogs = await transactionLogsQuery;

            const dto = (this.recordMapper as any).fromRecordToDto(
                record as TransactionRecord,
                transactionLogs as TransactionLogRecord[],
            ) as TransactionDto;
            results.push(dto);
        }

        return results;
    }

    async createTransactionLog(
        transactionId: string,
        transactionLog: TransactionLogDto,
        options: RepositoryOptions = {},
    ): Promise<void> {
        const client = this.db.getClient();
        const record = this.transactionLogMapper.fromDtoToRecord(transactionLog);
        const query = client.table(TableNames.TRANSACTION_LOG).insert({ ...record, transaction_id: transactionId });
        if (options.trx) query.transacting(options.trx);
        await query;
    }

    async updateTransactionLog(
        transactionId: string,
        logId: string,
        transactionLog: Partial<TransactionLogDto>,
        options: RepositoryOptions = {},
    ): Promise<void> {
        const client = this.db.getClient();
        const record = this.transactionLogMapper.fromDtoToRecord(transactionLog as TransactionLogDto);
        const query = client
            .table(TableNames.TRANSACTION_LOG)
            .where('transaction_id', transactionId)
            .where('id', logId)
            .update(record);
        if (options.trx) query.transacting(options.trx);
        await query;
    }

    async deleteTransactionLog(
        transactionId: string,
        logId: string,
        options: RepositoryOptions = {},
    ): Promise<void> {
        const client = this.db.getClient();
        const query = client
            .table(TableNames.TRANSACTION_LOG)
            .where('transaction_id', transactionId)
            .where('id', logId)
            .delete();
        if (options.trx) query.transacting(options.trx);
        await query;
    }
}
