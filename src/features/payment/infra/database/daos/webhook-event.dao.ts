import { WebhookEventDto } from '@features/payment/domain';
import { PAYMENT_MODULE_INJECT_TOKENS, TableNames } from 'src/shared';
import { Dao, DataSource, Lifecycle, Logger } from '@heronjs/common';
import { WebhookEventRecord } from '@features/payment/infra/database/records';
import { WebhookEventRecordMapper } from '@features/payment/infra/database/record-mappers';
import { BaseDao, IBaseDao, IDatabase, RepositoryOptions } from '@cbidigital/aqua-ddd';

export interface IWebhookEventDao extends IBaseDao<WebhookEventDto, WebhookEventRecord> {
    update(entity: Partial<WebhookEventDto>, options?: RepositoryOptions): Promise<Partial<WebhookEventDto>>;
    findByEventId(eventId: string, options?: RepositoryOptions): Promise<WebhookEventDto | null>;
    findByReferenceId(referenceId: string, options?: RepositoryOptions): Promise<WebhookEventDto[]>;
    findByGateway(gateway: string, options?: RepositoryOptions): Promise<WebhookEventDto[]>;
    findByStatus(status: string, options?: RepositoryOptions): Promise<WebhookEventDto[]>;
    findPendingEvents(options?: RepositoryOptions): Promise<WebhookEventDto[]>;
    findFailedEvents(options?: RepositoryOptions): Promise<WebhookEventDto[]>;
}

@Dao({
    token: PAYMENT_MODULE_INJECT_TOKENS.DAO.WEBHOOK_EVENT,
    scope: Lifecycle.Singleton,
})
export class WebhookEventDao
    extends BaseDao<WebhookEventDto, WebhookEventRecord>
    implements IWebhookEventDao
{
    private readonly logger = new Logger(this.constructor.name);

    constructor(@DataSource() db: IDatabase) {
        super({
            db,
            tableName: TableNames.WEBHOOK_EVENT,
            recordMapper: new WebhookEventRecordMapper(),
        });
    }

    async update(
        dto: Partial<WebhookEventDto>,
        options: RepositoryOptions = {},
    ): Promise<Partial<WebhookEventDto>> {
        const client = this.db.getClient();
        const record = this.recordMapper.fromDtoToRecord(dto as WebhookEventDto);
        const query = client.table(this.tableName).where('id', dto.id!).update(record);
        if (options.trx) query.transacting(options.trx);
        await query;
        return dto;
    }

    async findByEventId(eventId: string, options: RepositoryOptions = {}): Promise<WebhookEventDto | null> {
        const client = this.db.getClient();
        const query = client.table(this.tableName).where('event_id', eventId).first();
        if (options.trx) query.transacting(options.trx);
        const record = await query;

        if (!record) return null;

        return this.recordMapper.fromRecordToDto(record as WebhookEventRecord) as WebhookEventDto;
    }

    async findByReferenceId(
        referenceId: string,
        options: RepositoryOptions = {},
    ): Promise<WebhookEventDto[]> {
        const client = this.db.getClient();
        const query = client.table(this.tableName).where('reference_id', referenceId);
        if (options.trx) query.transacting(options.trx);
        const records = await query;

        return this.recordMapper.fromRecordsToDtos(records as WebhookEventRecord[]) as WebhookEventDto[];
    }

    async findByGateway(gateway: string, options: RepositoryOptions = {}): Promise<WebhookEventDto[]> {
        const client = this.db.getClient();
        const query = client.table(this.tableName).where('gateway', gateway);
        if (options.trx) query.transacting(options.trx);
        const records = await query;

        return this.recordMapper.fromRecordsToDtos(records as WebhookEventRecord[]) as WebhookEventDto[];
    }

    async findByStatus(status: string, options: RepositoryOptions = {}): Promise<WebhookEventDto[]> {
        const client = this.db.getClient();
        const query = client.table(this.tableName).where('status', status);
        if (options.trx) query.transacting(options.trx);
        const records = await query;

        return this.recordMapper.fromRecordsToDtos(records as WebhookEventRecord[]) as WebhookEventDto[];
    }

    async findPendingEvents(options: RepositoryOptions = {}): Promise<WebhookEventDto[]> {
        const client = this.db.getClient();
        const query = client.table(this.tableName).where('status', 'pending').orderBy('created_at', 'asc');
        if (options.trx) query.transacting(options.trx);
        const records = await query;

        return this.recordMapper.fromRecordsToDtos(records as WebhookEventRecord[]) as WebhookEventDto[];
    }

    async findFailedEvents(options: RepositoryOptions = {}): Promise<WebhookEventDto[]> {
        const client = this.db.getClient();
        const query = client.table(this.tableName).where('status', 'failed').orderBy('created_at', 'desc');
        if (options.trx) query.transacting(options.trx);
        const records = await query;

        return this.recordMapper.fromRecordsToDtos(records as WebhookEventRecord[]) as WebhookEventDto[];
    }
}
