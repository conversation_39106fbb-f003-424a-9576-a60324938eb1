import { Lifecycle, Provider } from '@heronjs/common';
import { PAYMENT_MODULE_INJECT_TOKENS } from 'src/shared';
import { BaseMapper, IMapper } from '@cbidigital/aqua-ddd';
import { TransactionDto, TransactionLogDto } from '@features/payment/domain/dtos';
import { Transaction, ITransaction } from '@features/payment/domain/aggregates/transaction/transaction';
import { TransactionLog } from '@features/payment/domain/aggregates/transaction/entities';

export type ITransactionMapper = IMapper<TransactionDto, ITransaction>;

@Provider({
    token: PAYMENT_MODULE_INJECT_TOKENS.MAPPER.TRANSACTION,
    scope: Lifecycle.Singleton,
})
export class TransactionMapper extends BaseMapper implements ITransactionMapper {
    constructor() {
        super();
    }

    async fromEntityToDto(entity: ITransaction): Promise<TransactionDto> {
        const transactionLogs: TransactionLogDto[] = entity.transactionLogs.map((log) => ({
            id: log.id,
            transactionId: log.transactionId,
            status: log.status,
            createdAt: log.createdAt,
            updatedAt: log.updatedAt,
        }));

        return {
            id: entity.id,
            gateway: entity.gateway,
            userId: entity.userId,
            gatewayTransactionId: entity.gatewayTransactionId,
            status: entity.status,
            transactionLogs,
            createdAt: entity.createdAt,
            updatedAt: entity.updatedAt,
        };
    }

    async fromDtoToEntity(dto: TransactionDto): Promise<ITransaction> {
        const transactionLogs = dto.transactionLogs.map(
            (logDto) =>
                new TransactionLog({
                    id: logDto.id,
                    props: {
                        id: logDto.id,
                        transactionId: logDto.transactionId,
                        status: logDto.status,
                        createdAt: logDto.createdAt,
                        updatedAt: logDto.updatedAt,
                    },
                }),
        );

        return new Transaction({
            id: dto.id,
            props: {
                id: dto.id,
                gateway: dto.gateway,
                userId: dto.userId,
                gatewayTransactionId: dto.gatewayTransactionId,
                status: dto.status,
                transactionLogs,
                createdAt: dto.createdAt,
                updatedAt: dto.updatedAt,
            },
        });
    }
}
