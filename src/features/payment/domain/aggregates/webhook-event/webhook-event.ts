import { randomUUID } from 'crypto';
import { Nullable } from '@heronjs/common';
import { AggregateRoot, AggregateRootConstructorPayload, IAggregateRoot } from '@cbidigital/aqua-ddd';
import {
    CreateWebhookEventInput,
    UpdateWebhookEventInput,
} from '@features/payment/domain/aggregates/webhook-event/types';

export type WebhookEventProps = {
    id: string;
    gateway: string;
    referenceId: string;
    eventId: string;
    eventType: string;
    payload: Record<string, any>;
    errorMessage: Nullable<string>;
    createdAt: Date;
    updatedAt: Nullable<Date>;
};

export type WebhookEventMethods = {
    create(payload: CreateWebhookEventInput): Promise<void>;
    update(payload: UpdateWebhookEventInput): Promise<void>;
};

export type IWebhookEvent = IAggregateRoot<WebhookEventProps, WebhookEventMethods>;

export class WebhookEvent
    extends AggregateRoot<WebhookEventProps, WebhookEventMethods>
    implements IWebhookEvent
{
    static AGGREGATE_NAME = 'webhook-event';

    constructor(payload: AggregateRootConstructorPayload<WebhookEventProps>) {
        super(payload);
    }

    /** Properties **/

    get id(): string {
        return this.props.id;
    }

    get gateway(): string {
        return this.props.gateway;
    }

    get referenceId(): string {
        return this.props.referenceId;
    }

    get eventId(): string {
        return this.props.eventId;
    }

    get eventType(): string {
        return this.props.eventType;
    }

    get payload(): Record<string, any> {
        return this.props.payload;
    }

    get errorMessage(): Nullable<string> {
        return this.props.errorMessage;
    }

    get createdAt(): Date {
        return this.props.createdAt;
    }

    get updatedAt(): Nullable<Date> {
        return this.props.updatedAt;
    }

    /** Methods **/

    protected setId(payload?: string): void {
        if (payload !== undefined) this.setProp('id', payload);
    }

    private setGateway(payload?: string): void {
        if (payload !== undefined) this.setProp('gateway', payload);
    }

    private setReferenceId(payload?: string): void {
        if (payload !== undefined) this.setProp('referenceId', payload);
    }

    private setEventId(payload?: string): void {
        if (payload !== undefined) this.setProp('eventId', payload);
    }

    private setEventType(payload?: string): void {
        if (payload !== undefined) this.setProp('eventType', payload);
    }

    private setPayload(payload?: Record<string, any>): void {
        if (payload !== undefined) this.setProp('payload', payload);
    }

    private setErrorMessage(payload?: Nullable<string>): void {
        if (payload !== undefined) this.setProp('errorMessage', payload);
    }

    private setCreatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('createdAt', payload);
    }

    private setUpdatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('updatedAt', payload);
    }

    async create(payload: CreateWebhookEventInput) {
        this.setId(randomUUID());
        this.setGateway(payload.gateway);
        this.setReferenceId(payload.referenceId);
        this.setEventId(payload.eventId);
        this.setEventType(payload.eventType);
        this.setPayload(payload.payload);
        this.setErrorMessage(payload.errorMessage || null);
        this.setCreatedAt(new Date());
    }

    async update(payload: UpdateWebhookEventInput) {
        this.setPayload(payload.payload);
        this.setErrorMessage(payload.errorMessage);
        this.setUpdatedAt(new Date());
    }
}
