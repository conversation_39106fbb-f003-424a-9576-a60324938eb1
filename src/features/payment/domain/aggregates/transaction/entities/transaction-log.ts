import { Nullable } from '@heronjs/common';
import { Entity, EntityConstructorPayload, IEntity } from '@cbidigital/aqua-ddd';
import { TransactionStatusEnum } from '@features/payment/domain/aggregates/transaction/enums';

export type TransactionLogProps = {
    id: string;
    transactionId: string;
    status: TransactionStatusEnum;
    createdAt: Date;
    updatedAt: Nullable<Date>;
};

export type TransactionLogMethods = {
    updateStatus(status: TransactionStatusEnum): void;
};

export type ITransactionLog = IEntity<TransactionLogProps, TransactionLogMethods>;

export class TransactionLog
    extends Entity<TransactionLogProps, TransactionLogMethods>
    implements ITransactionLog
{
    constructor(payload: EntityConstructorPayload<TransactionLogProps>) {
        super(payload);
    }

    /** Properties **/

    get id(): string {
        return this.props.id;
    }

    get transactionId(): string {
        return this.props.transactionId;
    }

    get status(): TransactionStatusEnum {
        return this.props.status;
    }

    get createdAt(): Date {
        return this.props.createdAt;
    }

    get updatedAt(): Nullable<Date> {
        return this.props.updatedAt;
    }

    /** Methods **/

    private setStatus(payload?: TransactionStatusEnum): void {
        if (payload !== undefined) this.setProp('status', payload);
    }

    private setUpdatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('updatedAt', payload);
    }

    updateStatus(status: TransactionStatusEnum): void {
        this.setStatus(status);
        this.setUpdatedAt(new Date());
    }
}
