import {
    TransactionErrorCodes,
    TransactionErrorNamespaces,
} from '@features/payment/domain/aggregates/transaction/errors/errors';
import { RuntimeError } from '@heronjs/common';

export class TransactionNotFoundError extends RuntimeError {
    constructor() {
        super(
            TransactionErrorNamespaces.TRANSACTION,
            TransactionErrorCodes.NOT_FOUND,
            'Transaction not found.',
        );
    }
}
