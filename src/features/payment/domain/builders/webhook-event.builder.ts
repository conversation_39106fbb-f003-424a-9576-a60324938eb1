import {
    AggregateRootBuilder,
    IAggregateRootBuilder,
    AggregateRootBuilderPayload,
} from '@cbidigital/aqua-ddd';
import { Lifecycle, Provider } from '@heronjs/common';
import { PAYMENT_MODULE_INJECT_TOKENS } from '@shared';
import { IWebhookEvent, WebhookEvent } from '@features/payment/domain/aggregates';

export type WebhookEventBuilderBuildPayload = AggregateRootBuilderPayload<IWebhookEvent>;
export type IWebhookEventBuilder = IAggregateRootBuilder<IWebhookEvent>;

@Provider({
    token: PAYMENT_MODULE_INJECT_TOKENS.BUILDER.WEBHOOK_EVENT,
    scope: Lifecycle.Singleton,
})
export class WebhookEventBuilder extends AggregateRootBuilder<IWebhookEvent> implements IWebhookEventBuilder {
    async build({ id, props, externalProps }: WebhookEventBuilderBuildPayload = {}): Promise<IWebhookEvent> {
        return new WebhookEvent({ id, props, externalProps });
    }
}
