import { PAYMENT_MODULE_INJECT_TOKENS } from '@shared';
import { ILogger, Inject, Lifecycle, Logger, Provider } from '@heronjs/common';
import { IWebhookHandler } from '@features/payment/app/handlers/webhook-handler.factory';
import {
    IDatabaseUtil,
    ITransactionBuilder,
    ITransactionRepository,
    IPaymentGatewayFactory,
    TransactionFailedEvent,
    TransactionNotFoundError,
} from '@features/payment/domain';

@Provider({
    token: PAYMENT_MODULE_INJECT_TOKENS.HANDLER.TRANSACTION_FAILED,
    scope: Lifecycle.Singleton,
})
export class TransactionFailedHandler implements IWebhookHandler {
    private readonly logger: ILogger;

    constructor(
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.BUILDER.TRANSACTION)
        protected readonly transactionBuilder: ITransactionBuilder,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.UTIL.DATABASE)
        protected readonly databaseUtil: IDatabaseUtil,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.TRANSACTION)
        protected readonly transactionRepo: ITransactionRepository,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.FACTORY.PAYMENT_GATEWAY)
        protected readonly paymentGatewayFactory: IPaymentGatewayFactory,
    ) {
        this.logger = new Logger(this.constructor.name);
    }

    async handle(event: TransactionFailedEvent) {
        const { data } = event;
        const { invoice } = data;
        this.logger.info('Handling transaction failed:::', invoice);
        const trx = await this.databaseUtil.startTrx();
        const repoOptions = { trx };
        try {
            const transaction = await this.transactionRepo.findOne(
                { filter: { gatewayTransactionId: { $eq: invoice } } },
                repoOptions,
            );
            if (!transaction) throw new TransactionNotFoundError();
            transaction.markAsFailed();
            await this.transactionRepo.update(transaction, repoOptions);
            await trx.commit();
        } catch (error) {
            await trx.rollback();
            this.logger.error('Failed to handle transaction failed', error);
            throw error;
        }
    }
}
