import z from 'zod';
import { PAYMENT_MODULE_INJECT_TOKENS } from '@shared';
import { WebhookEventDto } from '@features/payment/domain/dtos';
import { Inject, Lifecycle, Provider } from '@heronjs/common';
import { IUseCase, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';
import {
    IWebhookEventMapper,
    IWebhookEventRepository,
    WebhookEventNotFoundError,
} from '@features/payment/domain';

export type GetWebhookEventByEventIdUseCaseInput = {
    eventId: string;
};

export type GetWebhookEventByEventIdUseCaseOutput = WebhookEventDto;

const GetWebhookEventByEventIdUseCaseInputSchema = z.object({
    eventId: z.string().min(1, 'Event ID is required'),
});

export type IGetWebhookEventByEventIdUseCase = IUseCase<
    GetWebhookEventByEventIdUseCaseInput,
    GetWebhookEventByEventIdUseCaseOutput,
    UseCaseContext
>;

@Provider({
    token: PAYMENT_MODULE_INJECT_TOKENS.USECASE.GET_WEBHOOK_EVENT_BY_EVENT_ID,
    scope: Lifecycle.Transient,
})
export class GetWebhookEventByEventIdUseCase
    extends UseCase<
        GetWebhookEventByEventIdUseCaseInput,
        GetWebhookEventByEventIdUseCaseOutput,
        UseCaseContext
    >
    implements IGetWebhookEventByEventIdUseCase
{
    constructor(
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.WEBHOOK_EVENT)
        protected readonly repo: IWebhookEventRepository,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.MAPPER.WEBHOOK_EVENT)
        protected readonly mapper: IWebhookEventMapper,
    ) {
        super();
        this.setMethods(this.processing);
    }

    processing = async (input: GetWebhookEventByEventIdUseCaseInput) => {
        const { eventId } = GetWebhookEventByEventIdUseCaseInputSchema.parse(input);

        // Find the webhook event by event ID
        const webhookEvent = await this.repo.findOne({
            filter: { eventId: { $eq: eventId } },
        });

        if (!webhookEvent) throw new WebhookEventNotFoundError();

        // Convert to DTO for response
        return await this.mapper.fromEntityToDto(webhookEvent);
    };
}
